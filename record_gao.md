# 观测维度
    n_scan = 187
    n_priv_latent =  4 + 1 + 12 + 12 + 12 + 6 + 1 + 4 + 1 - 3 + 3 - 3 + 4 - 7 #47
    n_proprio = 45 + 3 # 3可能表示线速度
    history_len = 10
    num_observations = n_proprio + n_scan + history_len*n_proprio + n_priv_latent # 762  AMP的obs_buf只有45维

    num_prop, 48
    num_scan, 187
    num_critic_obs, 762
    num_priv_latent, 47  
    num_hist, 10
    num_actions, 12

    obs 762
    privileged_obs没有的
    critic_obs = obs 762

    priv_latent = torch.cat((
        #self.base_lin_vel * self.obs_scales.lin_vel,
        self.reindex_feet(self.contact_filt.float()-0.5),  4
        self.randomized_lag_tensor,    1
        #self.base_ang_vel  * self.obs_scales.ang_vel,  
        # self.base_lin_vel * self.obs_scales.lin_vel,   
        self.mass_params_tensor,  
        self.friction_coeffs_tensor,
        self.restitution_coeffs_tensor,
        self.motor_strength, 
        self.kp_factor,
        self.kd_factor), dim=-1)

    
# LeggedRobot
class BaseTask():增加self.cost_buf = torch.zeros(self.num_envs,self.num_costs,device=self.device,dtype=torch.float)
class LeggedRobot
    def _parse_cfg(self, cfg):
        self.dt = self.cfg.control.decimation * self.sim_params.dt
        self.obs_scales = self.cfg.normalization.obs_scales
        self.reward_scales = class_to_dict(self.cfg.rewards.scales)
        self.cost_scales = class_to_dict(self.cfg.costs.scales)
        self.cost_d_values = class_to_dict(self.cfg.costs.d_values)
        self.command_ranges = class_to_dict(self.cfg.commands.ranges)
        if self.cfg.terrain.mesh_type not in ['heightfield', 'trimesh']:
            self.cfg.terrain.curriculum = False
        self.max_episode_length_s = self.cfg.env.episode_length_s
        self.max_episode_length = np.ceil(self.max_episode_length_s / self.dt)
        
        # global counter 是否该类似这个
        self.cfg.domain_rand.push_interval = np.ceil(self.cfg.domain_rand.push_interval_s / self.dt)
    
        if self.cfg.env.history_encoding:
            self.obs_history_buf = torch.zeros(self.num_envs, self.cfg.env.history_len, self.cfg.env.n_proprio, device=self.device, dtype=torch.float)
        self.action_history_buf = torch.zeros(self.num_envs, self.cfg.env.history_len, self.num_dofs, device=self.device, dtype=torch.float)
        self.contact_buf = torch.zeros(self.num_envs, self.cfg.env.contact_buf_len, 4, device=self.device, dtype=torch.float) 好像没用
    

        #phase related
        self.phase = torch.zeros(self.num_envs, 4, dtype=torch.float, device=self.device,
                                        requires_grad=False)  # 表示每个环境中四个腿的当前相位角度（单位为弧度） 
        self.phase_time = torch.zeros(self.num_envs, 4, dtype=torch.float, device=self.device,
                                        requires_grad=False) # 记录每个腿当前处于该相位的时间长度
        self.frequency = 2. # 设置步态频率为 2Hz，即每秒完成两个完整的步态周期
        # Trot 步态定义 
        self.trot_gait = torch.zeros(1, 4, dtype=torch.float, device=self.device,requires_grad=False)
        self.trot_gait[:,0] = torch.pi  #  trot 步态的初始相位偏移   [FL (前左), FR (前右), RL (后左), RR (后右)] 对角腿同时摆动
        self.trot_gait[:,-1] = torch.pi
        print(self.trot_gait)

        self.trot_pattern1 = torch.tensor([1.,0,0,1.],dtype=torch.float, device=self.device,requires_grad=False).view(1,-1) #表示 FL 和 RR 处于支撑阶段（接触地面），FR 和 RL 处于摆动阶段；
        self.trot_pattern2 = torch.tensor([0.,1.,1.,0.],dtype=torch.float, device=self.device,requires_grad=False).view(1,-1)
 

# ActorCriticBarlowTwins 
是一个结合了 Actor-Critic 强化学习框架和 Barlow Twins 自监督学习方法的模型。从代码中可以分析出以下关键机制：

Actor-Critic 架构：
Actor 部分：生成动作策略，决定机器人应该采取什么动作
Critic 部分：评估状态价值，帮助 Actor 改进策略
Cost 函数：评估动作的代价，用于约束策略优化
Barlow Twins 自监督学习：
这是一种对比学习方法，通过最小化两个不同视角下同一数据的表示之间的冗余来学习有用的特征
在这个实现中，模型学习从历史状态序列预测当前状态的表示
损失函数包含三部分：
对角线损失：确保表示之间的相关性
非对角线损失：减少表示之间的冗余
私有状态损失：预测机器人的私有状态（如速度）
多模态输入处理：
处理多种输入：proprioceptive 状态（num_prop）、扫描数据（num_scan）和历史状态
使用不同的编码器处理不同类型的输入
历史状态编码：
使用 MlpBarlowTwinsActor 处理历史状态序列
通过 MLP 网络将历史状态编码为潜在表示
归一化技术：
使用批归一化（Batch Normalization）和经验归一化（Empirical Normalization）提高训练稳定性
混合模式：
支持教师模式和学生模式（通过 teacher_act 标志控制）
支持模仿学习（通过 imi_flag 标志控制）
这种架构的主要优势是：

通过自监督学习改善状态表示，减少对标记数据的依赖
能够从历史状态中提取有用信息，处理部分可观察环境
同时优化任务性能（通过 Actor-Critic）和表示学习（通过 Barlow Twins）
能够预测和利用私有状态信息，提高策略的鲁棒性
总的来说，这是一个将现代自监督学习技术与强化学习相结合的复杂架构，旨在提高机器人控制任务的性能和样本效率。

Value function input 282 num_prop48+self.scan_encoder_output_dim187+priv_encoder_output_dim47
cost function input 282

相当于policy:输入不涉及特权信息的地形信息 只包含历史信息
    def forward(self,obs,obs_hist):
        obs,obs_hist = self.normalize(obs,obs_hist)
        # with torch.no_grad():
        obs_hist_full = torch.cat([
                obs_hist[:,1:,:],
                obs.unsqueeze(1)
            ], dim=1)
        b,_,_ = obs_hist_full.size()
        # obs_hist_full = obs_hist_full[:,5:,:].view(b,-1)
        with torch.no_grad():
            latent = self.mlp_encoder(obs_hist_full[:,5:,:].reshape(b,-1))
            z = self.latent_layer(latent)
            vel = self.vel_layer(latent)
            # vel = self.history_encoder(obs_hist_full).detach()
            # #z = F.normalize(latents[:,3:],dim=-1,p=2).detach()
            # z = latents[:,3:].detach()
            # vel = latents[:,:3].detach()
        actor_input = torch.cat([vel.detach(),z.detach(),obs.detach()],dim=-1)
        mean  = self.actor(actor_input)
        # mean = self.actor(torch.cat([vel.detach(),z.detach()],dim=-1),obs.detach())
        return mean

python train.py --task=ysc4goN3poHim
python -m debugpy --listen 8934 --wait-for-client train.py --task=ysc4goN3poHim

python simple_play.py --task=ysc4goN3poHim

python play_joystick_.py --task=ysc4goN3poHim  --load_run=Jun13_16-40-50_test_barlowtwins

python play.py --task=ysc4goN3poHim


python play_joystick.py --task=robs3goN3poHim  --load_run=Jul09_16-38-34_test_barlowtwins