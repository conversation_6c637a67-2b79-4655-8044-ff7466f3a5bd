# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin

from configs.go2_constraint_him import Go2ConstraintHimRoughCfg, Go2ConstraintHimRoughCfgPPO
from configs.ysc4go_constraint_him import Ysc4goConstraintHimRoughCfg, Ysc4goConstraintHimRoughCfgPPO
from configs.yscgo_constraint_him import YscgoConstraintHimRoughCfg, YscgoConstraintHimRoughCfgPPO
from configs.robs3go_constraint_him import Robs3goConstraintHimRoughCfg, Robs3goConstraintHimRoughCfgPPO

from global_config import ROOT_DIR
import os
import time
import numpy as np
import cv2
from datetime import datetime
import pygame
from threading import Thread
import onnxruntime as ort

import isaacgym
from envs import *
from utils import get_args, export_policy_as_jit, task_registry, Logger,wrap_to_pi
from isaacgym.torch_utils import *
from isaacgym import gymtorch, gymapi, gymutil
import torch

# Global variables for joystick control
x_vel_cmd, y_vel_cmd, yaw_vel_cmd = 0.0, 0.0, 0.0
joystick_use = True
joystick_opened = False
HEADING_COMMAND = False
exit_flag = False
reset_pos = False
CAMERA_MODE = "free"


def quaternion_to_euler(q):
        """
        Convert a quaternion into euler angles (yaw, pitch, roll)
        Quaternion format: [x, y, z, w]
        """
        # Extract the values from the quaternion
        norm = torch.norm(q,dim = 1)
        norm = torch.where(norm == 0, 1e-8, norm.double())
        q = q / norm.unsqueeze(-1)
        x, y, z, w = q[:,0], q[:,1], q[:,2], q[:,3]

        # Calculate yaw (psi), pitch (theta), and roll (phi)
        t0 = +2.0 * (w * x + y * z)
        t1 = +1.0 - 2.0 * (x * x + y * y)
        roll_x = torch.atan2(t0, t1).unsqueeze(-1)
        
        t2 = +2.0 * (w * y - z * x)
        t2 = torch.clip(t2, -1.0 ,1.0)
        pitch_y = torch.asin(t2).unsqueeze(-1)
        
        t3 = +2.0 * (w * z + x * y)
        t4 = +1.0 - 2.0 * (y * y + z * z)
        yaw_z = torch.atan2(t3, t4).unsqueeze(-1)

        eular = torch.cat((roll_x,pitch_y,yaw_z),dim=-1)
        
        return eular

# 处理手柄输入的线程
def handle_joystick_input():
    global exit_flag, x_vel_cmd, y_vel_cmd, yaw_vel_cmd, head_vel_cmd, CAMERA_MODE, reset_pos

    X_button_was_pressed = False 
    A_button_was_pressed = False 

    for i in range(joystick.get_numbuttons()):
        button = joystick.get_button(i)
        print(f"Button {i}: {button}")

    if HEADING_COMMAND:
        while not exit_flag:
            # 获取手柄输入
            pygame.event.get()

            X_button_pressed = joystick.get_button(2)
            A_button_pressed = joystick.get_button(0)

            if X_button_pressed and not X_button_was_pressed:
                CAMERA_MODE = "follow" if CAMERA_MODE == "free" else "free"
                print(f"Camera mode switched to:{CAMERA_MODE}")
            X_button_was_pressed = X_button_pressed

            if A_button_pressed and not A_button_was_pressed:
                reset_pos = True
            A_button_was_pressed = A_button_pressed

            # 更新机器人命令
            x_vel_cmd = -joystick.get_axis(1) * 1.
            y_vel_cmd = -joystick.get_axis(0) * 1
            head_vel_cmd = -joystick.get_axis(3) * 3.14

            # print("AAAAA",x_vel_cmd, y_vel_cmd, head_vel_cmd)

            # 等待一小段时间，可以根据实际情况调整
            pygame.time.delay(100)
    else:
        while not exit_flag:
            # 获取手柄输入
            pygame.event.get()

            X_button_pressed = joystick.get_button(2)
            A_button_pressed = joystick.get_button(0)

            if X_button_pressed and not X_button_was_pressed:
                CAMERA_MODE = "follow" if CAMERA_MODE == "free" else "free"
                print(f"Camera mode switched to:{CAMERA_MODE}")
            X_button_was_pressed = X_button_pressed

            if A_button_pressed and not A_button_was_pressed:
                reset_pos = True
                print("press A")
            A_button_was_pressed = A_button_pressed
            
            # 更新机器人命令
            x_vel_cmd = -joystick.get_axis(1) * 2
            y_vel_cmd = -joystick.get_axis(0) * 1.5
            yaw_vel_cmd = -joystick.get_axis(3) * 6

            print(x_vel_cmd, y_vel_cmd, yaw_vel_cmd)

            # 等待一小段时间，可以根据实际情况调整
            pygame.time.delay(100)

    # 启动线程

    if joystick_opened and joystick_use:
        joystick_thread = Thread(target=handle_joystick_input)
        joystick_thread.start()
def play(args):
    global reset_pos
    env_cfg, train_cfg = task_registry.get_cfgs(name=args.task)
    # override some parameters for testing
    env_cfg.env.num_envs = min(env_cfg.env.num_envs, 1)
    env_cfg.terrain.num_rows = 10
    env_cfg.terrain.num_cols = 8
    env_cfg.terrain.curriculum = True
    env_cfg.terrain.max_init_terrain_level = 9
    env_cfg.noise.add_noise = False
    env_cfg.domain_rand.randomize_friction = False
    env_cfg.domain_rand.push_robots = False
    env_cfg.domain_rand.disturbance = False
    env_cfg.domain_rand.randomize_payload_mass = False
    env_cfg.commands.heading_command = False
    env_cfg.terrain.terrain_proportions=[0.2,0.2,0.2,0.2,0.2]
    

    # prepare environment
    env, _ = task_registry.make_env(name=args.task, args=args, env_cfg=env_cfg)
    env.max_episode_length=1000000
    obs = env.get_observations()

    # load policy
    # train_cfg.runner.resume = True
    # ppo_runner, train_cfg = task_registry.make_alg_runner(env=env, name=args.task, args=args, train_cfg=train_cfg)
    # policy = ppo_runner.get_actor_critic(device=env.device)

    onnx_path = os.path.join(ROOT_DIR, 'logs', train_cfg.runner.experiment_name, 'exported', 'policy_np3o.onnx')
    # onnx_path = "mlp_barlow_twins_actor.onnx"
    providers = ['CUDAExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
    session = ort.InferenceSession(onnx_path, providers=providers)

    # Get velocity scales from config
    lin_vel_scale = env_cfg.normalization.obs_scales.lin_vel
    ang_vel_scale = env_cfg.normalization.obs_scales.ang_vel

    # export policy as a jit module (used to run it from C++)
    # if EXPORT_POLICY:
    #     path = os.path.join(ROOT_DIR, 'logs', train_cfg.runner.experiment_name, 'exported', 'policies')
    #     export_policy_as_jit(ppo_runner.alg.actor_critic, path)
    #     print('Exported policy as jit script to: ', path)

    logger = Logger(env.dt)
    robot_index = np.random.randint(0,env_cfg.env.num_envs) 
    joint_index = 2  # which joint is used for logging
    stop_state_log = 5000  # number of steps before plotting states
    stop_rew_log = env.max_episode_length + 1  # number of steps before print average episode rewards
    print(env.max_episode_length)
    camera_position = np.array(env_cfg.viewer.pos, dtype=np.float64)
    camera_vel = np.array([1., 1., 0.])
    camera_direction = np.array(env_cfg.viewer.lookat) - np.array(env_cfg.viewer.pos)
    img_idx = 0
    env_ids = torch.tensor([0], device=env.device)
    # Initialize video recording if enabled
    video = None
    if RECORD_FRAMES:
        current_date_str = datetime.now().strftime('%Y-%m-%d')
        current_time_str = datetime.now().strftime('%H-%M-%S')
        frames_path = os.path.join(ROOT_DIR, 'logs',
                                 train_cfg.runner.experiment_name, 'exported_videos',
                                 current_date_str, current_time_str)
        os.makedirs(frames_path, exist_ok=True)

    # Initialize commands to zero
    env.commands[:, 0] = 0.0  # x velocity
    env.commands[:, 1] = 0.0  # y velocity
    env.commands[:, 2] = 0.0  # yaw velocity

    obs_prop = np.zeros((1, 45), dtype=np.float32)
    obs_hist = np.zeros((1, 10, 45), dtype=np.float32)

    try:
        for i in range(10*int(env.max_episode_length)):
            # Update commands from joystick
            env.commands[:, 0] = x_vel_cmd
            env.commands[:, 1] = y_vel_cmd
            env.commands[:, 2] = yaw_vel_cmd

            # actions = policy.act_teacher(obs.detach())
            # obs_prop = obs[:, 3:48].detach().cpu().numpy()
            # obs_hist = obs[:, -10*48:].view(-1, 10, 48)[:,:,3:].detach().cpu().numpy()
            outputs = session.run(None, {'obs': obs_prop.astype(np.float32),'hist':obs_hist.astype(np.float32)})
            actions = torch.from_numpy(outputs[0]).to(env.device)
            # print(actions)

            if args.task.endswith('_amp'):
                obs, _, rews, dones, infos, _, _, _= env.step(actions.detach())
            else:
                obs, privileged_obs, rewards,costs,dones, infos = env.step(actions.detach())
            
            obs_components = torch.cat((
                env.base_ang_vel * env.obs_scales.ang_vel,  # 3
                env.projected_gravity,  # 3
                env.commands[:, :3] * env.commands_scale,  # 3
                (env.dof_pos - env.default_dof_pos) * env.obs_scales.dof_pos,  # 12
                env.dof_vel * env.obs_scales.dof_vel,  # 12
                actions) ,# 12
                 dim=-1)
            obs_prop=obs_components.cpu().numpy()

            if i==0:
                obs_hist = np.repeat(obs_prop, 10, axis=0)  
                obs_hist = obs_hist[np.newaxis, ...]  
            else:
                obs_hist = np.roll(obs_hist, shift=-1, axis=1)
                obs_hist[0, -1] = obs_prop
              

            if RECORD_FRAMES:
                if i % 2:
                    filename = os.path.join(frames_path, f"{img_idx}.png")
                    env.gym.write_viewer_image_to_file(env.viewer, filename)
                    img = cv2.imread(filename)
                    if video is None:
                        video = cv2.VideoWriter(os.path.join(frames_path, 'record_joystick.mp4'),
                                              cv2.VideoWriter_fourcc(*'MP4V'),
                                              int(1 / env.dt), (img.shape[1], img.shape[0]))
                    video.write(img)
                    img_idx += 1

            if CAMERA_MODE == "follow":
                # camera_position += camera_vel * env.dt
                # env.set_camera(camera_position, camera_position + camera_direction)
                look_at = np.array(env.root_states[0, :3].cpu(), dtype=np.float64)
                # camera_rot = (camera_rot + camera_rot_per_sec * env.dt) % (2 * np.pi)
                yaw = quaternion_to_euler(env.root_states[:, 3:7])[:,2]
                camera_rot = yaw.squeeze(0).cpu().numpy() + np.pi
                camera_relative_position = 2.0 * np.array(
                        [np.cos(camera_rot), np.sin(camera_rot), 0.45])
                env.set_camera(look_at + camera_relative_position, look_at)

            if reset_pos == True:    
                last_root_states =  env.root_states.clone( )  
                env.root_states[0,:] = env.base_init_state
                env.root_states[:, :2] += last_root_states[:,:2]
                env.root_states[:, 2:3] += last_root_states[:,2:3] + 2 * env_cfg.rewards.base_height_target
                env_ids_int32 = env_ids.to(dtype=torch.int32)
                env.gym.set_actor_root_state_tensor_indexed(
                        env.sim, gymtorch.unwrap_tensor(env.root_states),
                        gymtorch.unwrap_tensor(env_ids_int32), len(env_ids_int32))
                print("reset_pos:",reset_pos)
                reset_pos = False

            if i < stop_state_log:
                logger.log_states(
                    {
                        'dof_pos_target': actions[robot_index, joint_index].item() * env.cfg.control.action_scale + env.default_dof_pos[robot_index, joint_index].item(),
                        'dof_pos': env.dof_pos[robot_index, joint_index].item(),
                        'dof_vel': env.dof_vel[robot_index, joint_index].item(),
                        'dof_torque': env.torques[robot_index, joint_index].item(),
                        'command_x': env.commands[robot_index, 0].item(),
                        'command_y': env.commands[robot_index, 1].item(),
                        'command_yaw': env.commands[robot_index, 2].item(),
                        'base_vel_x': env.base_lin_vel[robot_index, 0].item(),
                        'base_vel_y': env.base_lin_vel[robot_index, 1].item(),
                        'base_vel_z': env.base_lin_vel[robot_index, 2].item(),
                        'base_vel_yaw': env.base_ang_vel[robot_index, 2].item(),
                        'contact_forces_z': env.contact_forces[robot_index, env.feet_indices, 2].cpu().numpy()
                    }
                )
            elif i==stop_state_log:
                logger.plot_states()
            elif i==stop_rew_log:
                logger.print_rewards()

    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        if RECORD_FRAMES and video is not None:
            video.release()
            # Clean up individual frames
            for file in os.listdir(frames_path):
                if file.endswith(".png"):
                    os.remove(os.path.join(frames_path, file))
        exit_flag = True
        if joystick_opened:
            pygame.quit()

if __name__ == '__main__':
    EXPORT_POLICY = True
    RECORD_FRAMES = False
    MOVE_CAMERA = False

    task_registry.register("go2N3poHim",LeggedRobot,Go2ConstraintHimRoughCfg(),Go2ConstraintHimRoughCfgPPO())
    task_registry.register("ysc4goN3poHim",LeggedRobot,Ysc4goConstraintHimRoughCfg(),Ysc4goConstraintHimRoughCfgPPO())
    task_registry.register("yscgoN3poHim",LeggedRobot,YscgoConstraintHimRoughCfg(),YscgoConstraintHimRoughCfgPPO())
    task_registry.register("robs3goN3poHim",LeggedRobot,Robs3goConstraintHimRoughCfg(),Robs3goConstraintHimRoughCfgPPO())

  
    args = get_args()
    # For inference, we don't need to load optimizer
    args.load_optimizer = False

    # Initialize pygame and joystick
    if joystick_use:
        pygame.init()
        try:
            joystick = pygame.joystick.Joystick(0)
            joystick.init()
            joystick_opened = True
            print("Joystick initialized successfully")
            
            # Start joystick input thread
            joystick_thread = Thread(target=handle_joystick_input)
            joystick_thread.start()
        except Exception as e:
            print(f"Failed to initialize joystick: {e}")
            joystick_use = False

    play(args) 