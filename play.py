# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin

# from legged_gym import LEGGED_GYM_ROOT_DIR
# import os

# import isaacgym
# from legged_gym.envs import *
# from legged_gym.utils import  get_args, export_policy_as_jit, task_registry, Logger
from configs.go2_constraint_him import Go2ConstraintHimRoughCfg, Go2ConstraintHimRoughCfgPPO
from configs.ysc4go_constraint_him import Ysc4goConstraintHimRoughCfg, Ysc4goConstraintHimRoughCfgPPO
from configs.yscgo_constraint_him import YscgoConstraintHimRoughCfg, YscgoConstraintHimRoughCfgPPO
from configs.robs3go_constraint_him import Robs3goConstraintHimRoughCfg, Robs3goConstraintHimRoughCfgPPO

from global_config import ROOT_DIR
from isaacgym import gymapi
from utils import  get_args, export_policy_as_jit, task_registry, Logger
from envs import *

import numpy as np
import torch
import cv2
import os


def play(args, x_vel=1.0, y_vel=0.0, yaw_vel=0.0):
    env_cfg, train_cfg = task_registry.get_cfgs(name=args.task)
    # override some parameters for testing
    env_cfg.env.num_envs = min(env_cfg.env.num_envs, 1)
    env_cfg.terrain.num_rows = 5
    env_cfg.terrain.num_cols = 5
    env_cfg.terrain.curriculum = True
    env_cfg.terrain.max_init_terrain_level = 9
    env_cfg.init_state.randomize_start_pose = False
    env_cfg.noise.add_noise = False
    env_cfg.domain_rand.randomize_friction = False
    env_cfg.domain_rand.push_robots = False
    env_cfg.domain_rand.disturbance = False
    env_cfg.domain_rand.randomize_payload_mass = False
    env_cfg.commands.heading_command = False
    # env_cfg.terrain.mesh_type = 'plane'
    # prepare environment
    env, _ = task_registry.make_env(name=args.task, args=args, env_cfg=env_cfg)
    env.commands[:, 0] = x_vel
    env.commands[:, 1] = y_vel
    env.commands[:, 2] = yaw_vel
    env.commands[:, 2] = 0.0

    obs = env.get_observations()

    # load policy
    train_cfg.runner.resume = True
    ppo_runner, train_cfg = task_registry.make_alg_runner(env=env, name=args.task, args=args, train_cfg=train_cfg)
    policy = ppo_runner.get_actor_critic(device=env.device)


    # export policy as a jit module (used to run it from C++)
    if EXPORT_POLICY:
        path = os.path.join(ROOT_DIR, 'logs', train_cfg.runner.experiment_name, 'exported')
        os.makedirs(path, exist_ok=True)
        jit_path = os.path.join(path, 'policy.pt')
        policy.save_torch_jit_policy(jit_path,env.device)
        print('Exported policy as jit script to: ', jit_path)
     
        policy = policy.to("cpu")
        policy.eval()
        onnx_path=os.path.join(path, 'policy.onnx')
        policy.save_torch_onnx_policy(onnx_path)
        print('Exported policy as onnx script to: ', onnx_path)
        
        policy = policy.to(env.device)

        print("Verifying ONNX export...")
        try:
            import onnxruntime as ort

            # Load ONNX model
            ort_session = ort.InferenceSession(onnx_path)

            # Test with a few random observations
            num_tests = 5
            max_diff = 0.0

            for i in range(num_tests):
                # Generate test observation
                test_obs = torch.randn(1, obs.shape[-1], dtype=torch.float32, device='cuda:0')

                # Get original agent output
                with torch.no_grad():
                    original_action = policy.act_teacher(test_obs.detach())

                # Get ONNX model output
                obs_numpy = test_obs.cpu().numpy()
                obs=obs_numpy[:,3:48]
                hist_obs=obs_numpy[:,-480:].reshape(obs_numpy.shape[0],10,48)[:,:,3:]
                ort_inputs = {ort_session.get_inputs()[0].name: obs,ort_session.get_inputs()[1].name: hist_obs}
                onnx_action = ort_session.run(None, ort_inputs)[0]

                # Compare
                original_action_numpy = original_action.cpu().numpy()
                diff = np.abs(original_action_numpy - onnx_action).max()
                max_diff = max(max_diff, diff)

                print(f"Test {i+1}:")
                print(f"  Original: {original_action_numpy.flatten()[:]}...")
                print(f"  ONNX:     {onnx_action.flatten()[:]}...")
                print(f"  Max diff: {diff:.8f}")

        except ImportError:
            print("Warning: onnxruntime not available for verification")
            print("Install with: pip install onnxruntime")
        except Exception as e:
            print(f"ONNX verification failed: {e}")

    logger = Logger(env.dt)
    robot_index = 0 # which robot is used for logging
    joint_index = 1 # which joint is used for logging
    stop_state_log = 100 # number of steps before plotting states
    stop_rew_log = env.max_episode_length + 1 # number of steps before print average episode rewards
    camera_position = np.array(env_cfg.viewer.pos, dtype=np.float64)
    camera_vel = np.array([1., 1., 0.])
    camera_direction = np.array(env_cfg.viewer.lookat) - np.array(env_cfg.viewer.pos)
    img_idx = 0

    for i in range(10*int(env.max_episode_length)):
    
        actions = policy.act_teacher(obs.detach())
        env.commands[:, 0] = x_vel
        env.commands[:, 1] = y_vel
        env.commands[:, 2] = yaw_vel
        env.commands[:,3] = 0
        # obs, _, rews, dones, infos, _, _ = env.step(actions.detach())  #[n 270]
        if args.task.endswith('_amp'):
            obs, _, rews, dones, infos, _, _, _= env.step(actions.detach())
        else:
            obs, privileged_obs, rewards,costs,dones, infos = env.step(actions.detach())

        if RECORD_FRAMES:
            if i % 2:
                filename = os.path.join(ROOT_DIR, 'logs', train_cfg.runner.experiment_name, 'exported', 'frames', f"{img_idx}.png")
                env.gym.write_viewer_image_to_file(env.viewer, filename)
                img_idx += 1 
        if MOVE_CAMERA:
            camera_position += camera_vel * env.dt
            env.set_camera(camera_position, camera_position + camera_direction)

        if i < stop_state_log:
            logger.log_states(
                {
                    'dof_pos_target': actions[robot_index, joint_index].item() * env.cfg.control.action_scale + env.default_dof_pos[robot_index, joint_index].item(),
                    'dof_pos': env.dof_pos[robot_index, joint_index].item(),
                    'dof_vel': env.dof_vel[robot_index, joint_index].item(),
                    'dof_torque': env.torques[robot_index, joint_index].item(),
                    'command_x': env.commands[robot_index, 0].item(),
                    'command_y': env.commands[robot_index, 1].item(),
                    'command_yaw': env.commands[robot_index, 2].item(),
                    'base_vel_x': env.base_lin_vel[robot_index, 0].item(),
                    'base_vel_y': env.base_lin_vel[robot_index, 1].item(),
                    'base_vel_z': env.base_lin_vel[robot_index, 2].item(),
                    'base_vel_yaw': env.base_ang_vel[robot_index, 2].item(),
                    'contact_forces_z': env.contact_forces[robot_index, env.feet_indices, 2].cpu().numpy()
                }
            )
        elif i==stop_state_log:
            logger.plot_states()
        if  0 < i < stop_rew_log:
            if infos["episode"]:
                num_episodes = torch.sum(env.reset_buf).item()
                if num_episodes>0:
                    logger.log_rewards(infos["episode"], num_episodes)
        elif i==stop_rew_log:
            logger.print_rewards()

if __name__ == '__main__':
    EXPORT_POLICY = True
    RECORD_FRAMES = False
    MOVE_CAMERA = False
    task_registry.register("go2N3poHim",LeggedRobot,Go2ConstraintHimRoughCfg(),Go2ConstraintHimRoughCfgPPO())
    task_registry.register("ysc4goN3poHim",LeggedRobot,Ysc4goConstraintHimRoughCfg(),Ysc4goConstraintHimRoughCfgPPO())
    task_registry.register("yscgoN3poHim",LeggedRobot,YscgoConstraintHimRoughCfg(),YscgoConstraintHimRoughCfgPPO())
    task_registry.register("robs3goN3poHim",LeggedRobot,Robs3goConstraintHimRoughCfg(),Robs3goConstraintHimRoughCfgPPO())
  
    args = get_args()
    play(args, x_vel=1.0, y_vel=0.0, yaw_vel=0.0)
