# import torch
# import torch.nn as nn
# import sys

# from modules.actor_critic import MlpBarlowTwinsActor


# batch_size = 1
# num_prop = 45  
# num_hist = 10 
# # 创建示例输入
# obs = torch.randn(batch_size, num_prop)
# obs_hist = torch.randn(batch_size, num_hist, num_prop)

# # # 实例化模型
# model = MlpBarlowTwinsActor(
#     num_prop=num_prop-3,
#     num_hist=5,  
#     num_actions=12,
#     actor_dims=[512,256,128],
#     mlp_encoder_dims=[128,64],
#     activation=nn.ELU(),
#     latent_dim=16,
#     obs_encoder_dims=[128,64]
# )

# # 加载预训练权重
# # checkpoint_path = "logs/rough_ysc4go_constraint/exported/policy.pt"  # 替换为实际路径
# # model = torch.jit.load(checkpoint_path)
# # model.eval()

# checkpoint_path = "logs/rough_ysc4go_constraint/exported/policy.pt"  # 替换为实际路径
# # checkpoint = torch.load(checkpoint_path, map_location=torch.device("cpu"))

# try:
#     model = torch.jit.load(checkpoint_path)
#     model.eval()
#     print(f"Successfully loaded model from {checkpoint_path}")
#     print(f"Model structure: {model}")
# except Exception as e:
#     print(f"Error loading model: {e}")
#     sys.exit(1)

# # # 尝试加载 state_dict，处理常见格式
# # try:
# #     model.load_state_dict(checkpoint)
# # except RuntimeError as e:
# #     print("直接加载失败，尝试处理键名...")
# #     # 可能包含 'model_state_dict' 或 'module.' 前缀
# #     if isinstance(checkpoint, dict):
# #         state_dict = checkpoint.get('model_state_dict', checkpoint)
# #     else:
# #         state_dict = checkpoint
# #     # 移除 'module.' 前缀（若使用 DataParallel）
# #     state_dict = {k.replace("module.", ""): v for k, v in state_dict.items()}
# #     model.load_state_dict(state_dict, strict=False)

# # 验证模型输出
# with torch.no_grad():
#     pytorch_output = model(obs, obs_hist)
# print("PyTorch 输出:", pytorch_output)

# # 导出 ONNX 模型
# torch.onnx.export(
#     model,
#     (obs, obs_hist),
#     "mlp_barlow_twins_actor.onnx",
#     export_params=True,
#     opset_version=12,
#     do_constant_folding=True,
#     input_names=['obs', 'obs_hist'],
#     output_names=['mean'],
# )

# print("ONNX 模型已导出至 mlp_barlow_twins_actor.onnx")

# # 可选：验证 ONNX 模型
# import onnxruntime as ort
# import numpy as np

# session = ort.InferenceSession("mlp_barlow_twins_actor.onnx")
# obs_np = obs.numpy()
# obs_hist_np = obs_hist.numpy()
# inputs = {'obs': obs_np, 'obs_hist': obs_hist_np}
# outputs = session.run(None, inputs)
# print("ONNX 输出:", outputs[0])

# with torch.no_grad():
#     pytorch_output = model(obs, obs_hist).numpy()
# print("PyTorch 输出:", pytorch_output)
# print("输出差异:", np.abs(outputs[0] - pytorch_output).max())


import torch
import torch.onnx
import onnx
import onnxruntime as ort
import numpy as np

# 示例输入
batch_size = 1
num_prop = 45
num_hist = 10

obs = torch.randn(batch_size, num_prop)
obs_hist = torch.randn(batch_size, num_hist, num_prop)

# 加载 TorchScript 模型
model_path = "logs/rough_ysc4go_constraint/exported/policy.pt"
try:
    model = torch.jit.load(model_path,map_location="cpu")
    model.eval()
except Exception as e:
    raise RuntimeError(f"无法加载模型: {e}")

# Tracing 模型（如果尚未 trace）
try:
    traced_model = torch.jit.trace(model, (obs, obs_hist))
    print("TorchScript 模型已成功 trace")
except Exception as e:
    raise RuntimeError(f"Trace 失败: {e}")

# 导出为 ONNX
onnx_output_path = "mlp_barlow_twins_actor.onnx"

try:
    torch.onnx.export(
        traced_model,
        (obs, obs_hist),
        onnx_output_path,
        export_params=True,          # 存储训练参数
        opset_version=13,           # ONNX 算子集版本
        # do_constant_folding=True,   # 优化常量
        input_names=['obs', 'obs_hist'],
        output_names=['mean'],
    )
    print(f"ONNX 模型已成功导出至 {onnx_output_path}")
except Exception as e:
    raise RuntimeError(f"导出 ONNX 失败: {e}")

# 可选：验证 ONNX 模型是否正确
try:
    ort_session = ort.InferenceSession(onnx_output_path)

    # 将输入转换为 NumPy 数组
    inputs = {
        'obs': obs.numpy(),
        'obs_hist': obs_hist.numpy()
    }

    # 推理
    outputs = ort_session.run(None, inputs)
    print("ONNX Runtime 输出:", outputs[0])

    # 对比 PyTorch 输出
    with torch.no_grad():
        pt_output = model(obs, obs_hist).numpy()

    print("PyTorch 输出:", pt_output)
    print("最大误差:", np.max(np.abs(outputs[0] - pt_output)))

except Exception as e:
    print(f"ONNX 验证失败: {e}")