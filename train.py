import numpy as np
import os
from datetime import datetime
from configs.go2_constraint_him import Go2ConstraintHimRoughCfg, Go2ConstraintHimRoughCfgPPO
from configs.ysc4go_constraint_him import Ysc4goConstraintHimRoughCfg, Ysc4goConstraintHimRoughCfgPPO
from configs.yscgo_constraint_him import YscgoConstraintHimRoughCfg, YscgoConstraintHimRoughCfgPPO
from configs.robs3go_constraint_him import Robs3goConstraintHimRoughCfg, Robs3goConstraintHimRoughCfgPPO


import isaacgym
from utils.helpers import get_args
from envs import LeggedRobot
from utils.task_registry import task_registry

def train(args):
    env, env_cfg = task_registry.make_env(name=args.task, args=args)
    ppo_runner, train_cfg = task_registry.make_alg_runner(env=env, name=args.task, args=args)
    ppo_runner.learn(num_learning_iterations=train_cfg.runner.max_iterations, init_at_random_ep_len=True)

if __name__ == '__main__':
    task_registry.register("go2N3poHim",LeggedRobot,Go2ConstraintHimRoughCfg(),Go2ConstraintHimRoughCfgPPO())
    task_registry.register("ysc4goN3poHim",LeggedRobot,Ysc4goConstraintHimRoughCfg(),Ysc4goConstraintHimRoughCfgPPO())
    task_registry.register("yscgoN3poHim",LeggedRobot,YscgoConstraintHimRoughCfg(),YscgoConstraintHimRoughCfgPPO())
    task_registry.register("robs3goN3poHim",LeggedRobot,Robs3goConstraintHimRoughCfg(),Robs3goConstraintHimRoughCfgPPO())
    args = get_args()
    train(args)
