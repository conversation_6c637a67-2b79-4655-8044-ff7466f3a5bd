<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /media/lmm/myxt/Gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_ysc4go/legged_ysc4go_description/urdf/robot.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="robot">
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.4235294117647059 0.0392156862745098 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.8705882352941177 0.8117647058823529 0.7647058823529411 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
 
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/base_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.238 0.18 0.14"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00188996 -0.00029154 0.00533717"/>
      <mass value="5.0413214"/>
      <inertia ixx="0.01870468" ixy="-4.34e-06" ixz="-0.00022072" iyy="0.06161216" iyz="-6.347e-05" izz="0.07105171"/>
    </inertial>
  </link>
  <!-- Imu is fixed to the base link -->
  <joint name="base_imu_joint" type="fixed">
    <origin rpy="0. 0. 0." xyz="0. 0. 0."/>
    <parent link="base"/>
    <child link="base_imu"/>
  </joint>
  <!-- Imu link -->
  <link name="base_imu">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.000001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.000001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.015 0.015 0.004"/>
      </geometry>
    </visual>
    <material name="orange">
      <color rgba="255 108 10 255"/>
    </material>
  </link>

  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.1975 -0.0525 -0.0"/>
    <parent link="base"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.8377580409572781" upper="0.8377580409572781" velocity="24.18"/>
  </joint>
  <link name="FR_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <!-- <origin rpy="${pi} 0 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/R_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00277777 0.00184525 5.127e-05"/>
      <mass value="0.55678826"/>
      <inertia ixx="0.0003018" ixy="1.398e-05" ixz="1.4e-07" iyy="0.00038529" iyz="1.04e-06" izz="0.00036015"/>
    </inertial>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.1042 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="2.743657584135086" velocity="24.18"/>
  </joint>
  <link name="FR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/R_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.198 0.03 0.03"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0032359 0.03107324 -0.02091443"/>
      <mass value="0.86351833"/>
      <inertia ixx="0.00305713" ixy="-9.633e-05" ixz="-0.00023416" iyy="0.00270567" iyz="-0.00055739" izz="0.00092055"/>
    </inertial>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="30.5" lower="-2.740166925631097" upper="-0.9040805525330626" velocity="15.49"/>
  </joint>
  <link name="FR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <geometry>
        <box size="0.198 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00046755 0.0 -0.10961712"/>
      <mass value="0.14794328"/>
      <inertia ixx="0.00090401" ixy="-1e-08" ixz="2.397e-05" iyy="0.00091303" iyz="-1.6e-07" izz="3.274e-05"/>
    </inertial>
  </link>
  <joint name="FR_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="FR_calf"/>
    <child link="FR_foot"/>
  </joint>
  <link name="FR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
        <!-- <mesh filename="package://legged_ysc4go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="9.809511956992001e-06" ixy="0.0" ixz="0.0" iyy="9.809511956992001e-06" iyz="0.0" izz="9.809511956992001e-06"/>
    </inertial>

  </link>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.1975 0.0525 -0.0"/>
    <parent link="base"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.8377580409572781" upper="0.8377580409572781" velocity="24.18"/>
  </joint>
  <link name="FL_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00277777 -0.00184525 5.127e-05"/>
      <mass value="0.55678826"/>
      <inertia ixx="0.0003018" ixy="-1.398e-05" ixz="1.4e-07" iyy="0.00038529" iyz="-1.04e-06" izz="0.00036015"/>
    </inertial>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.1042 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="2.743657584135086" velocity="24.18"/>
  </joint>
  <link name="FL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.198 0.03 0.03"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0032359 -0.03107324 -0.02091443"/>
      <mass value="0.86351833"/>
      <inertia ixx="0.00305713" ixy="9.633e-05" ixz="-0.00023416" iyy="0.00270567" iyz="0.00055739" izz="0.00092055"/>
    </inertial>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="30.5" lower="-2.740166925631097" upper="-0.9040805525330626" velocity="15.49"/>
  </joint>
  <link name="FL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <geometry>
        <box size="0.198 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00046755 0.0 -0.10961712"/>
      <mass value="0.14794328"/>
      <inertia ixx="0.00090401" ixy="-1e-08" ixz="2.397e-05" iyy="0.00091303" iyz="-1.6e-07" izz="3.274e-05"/>
    </inertial>
  </link>
  <joint name="FL_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="FL_calf"/>
    <child link="FL_foot"/>
  </joint>
  <link name="FL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
        <!-- <mesh filename="package://legged_ysc4go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="9.809511956992001e-06" ixy="0.0" ixz="0.0" iyy="9.809511956992001e-06" iyz="0.0" izz="9.809511956992001e-06"/>
    </inertial>
  </link>
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.1975 -0.0525 -0.0"/>
    <parent link="base"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.8377580409572781" upper="0.8377580409572781" velocity="24.18"/>
  </joint>
  <link name="RR_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <!-- <origin rpy="${pi} ${pi} 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00277777 0.00184525 5.127e-05"/>
      <mass value="0.55678826"/>
      <inertia ixx="0.0003018" ixy="-1.398e-05" ixz="-1.4e-07" iyy="0.00038529" iyz="1.04e-06" izz="0.00036015"/>
    </inertial>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.1042 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="2.743657584135086" velocity="24.18"/>
  </joint>
  <link name="RR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/R_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.198 0.03 0.03"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0032359 0.03107324 -0.02091443"/>
      <mass value="0.86351833"/>
      <inertia ixx="0.00305713" ixy="-9.633e-05" ixz="-0.00023416" iyy="0.00270567" iyz="-0.00055739" izz="0.00092055"/>
    </inertial>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="30.5" lower="-2.740166925631097" upper="-0.9040805525330626" velocity="15.49"/>
  </joint>
  <link name="RR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <geometry>
        <box size="0.198 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00046755 0.0 -0.10961712"/>
      <mass value="0.14794328"/>
      <inertia ixx="0.00090401" ixy="-1e-08" ixz="2.397e-05" iyy="0.00091303" iyz="-1.6e-07" izz="3.274e-05"/>
    </inertial>
  </link>
  <joint name="RR_foot_fixed" type="fixed"  dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="RR_calf"/>
    <child link="RR_foot"/>
  </joint>
  <link name="RR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
        <!-- <mesh filename="package://legged_ysc4go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="9.809511956992001e-06" ixy="0.0" ixz="0.0" iyy="9.809511956992001e-06" iyz="0.0" izz="9.809511956992001e-06"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.1975 0.0525 -0.0"/>
    <parent link="base"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.8377580409572781" upper="0.8377580409572781" velocity="24.18"/>
  </joint>
  <link name="RL_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <!-- <origin rpy="0 ${pi} 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/R_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00277777 -0.00184525 5.127e-05"/>
      <mass value="0.55678826"/>
      <inertia ixx="0.0003018" ixy="1.398e-05" ixz="-1.4e-07" iyy="0.00038529" iyz="-1.04e-06" izz="0.00036015"/>
    </inertial>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.1042 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="2.743657584135086" velocity="24.18"/>
  </joint>
  <link name="RL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.198 0.03 0.03"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0032359 -0.03107324 -0.02091443"/>
      <mass value="0.86351833"/>
      <inertia ixx="0.00305713" ixy="9.633e-05" ixz="-0.00023416" iyy="0.00270567" iyz="0.00055739" izz="0.00092055"/>
    </inertial>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="30.5" lower="-2.740166925631097" upper="-0.9040805525330626" velocity="15.49"/>
  </joint>
  <link name="RL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="resources/ysc4go/meshes/ysc4go/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.099"/>
      <geometry>
        <box size="0.198 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00046755 0.0 -0.10961712"/>
      <mass value="0.14794328"/>
      <inertia ixx="0.00090401" ixy="-1e-08" ixz="2.397e-05" iyy="0.00091303" iyz="-1.6e-07" izz="3.274e-05"/>
    </inertial>
  </link>
  <joint name="RL_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.198"/>
    <parent link="RL_calf"/>
    <child link="RL_foot"/>
  </joint>
  <link name="RL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
        <!-- <mesh filename="package://legged_ysc4go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02332"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="9.809511956992001e-06" ixy="0.0" ixz="0.0" iyy="9.809511956992001e-06" iyz="0.0" izz="9.809511956992001e-06"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  <!-- Robot Footprint -->
  <!-- <joint name="footprint_joint" type="fixed">
    <parent link="base"/>
    <child link="base_footprint"/>
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.2"/>
  </joint>
  <link name="base_footprint">
    <inertial>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
      <mass value="0.001"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link> -->
  <!-- <xacro:lidar parent="base" xyz="-0.065 0. 0.1" rpy="0. 0. 0."/> -->
</robot>

