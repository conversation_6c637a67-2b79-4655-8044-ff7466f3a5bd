<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot.xacro                    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="robot">
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="gold">
    <color rgba="0.94901960784 0.75294117647 0.33725490196 0.0"/> 
  </material>
  <material name="grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.4235294117647059 0.0392156862745098 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.8705882352941177 0.8117647058823529 0.7647058823529411 1.0"/>
  </material>
  <material name="red">
  <!-- aa0505 -->
    <!--color rgba="0.66666666667 0.01960784314 0.01960784314 1.0"/-->
    <color rgba="0.54901960784 0.04705882353 0.0431372549  1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/base_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.238 0.18 0.14"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00051375 -0.00026111 -0.00543225"/>
      <mass value="5.19760345"/>
      <inertia ixx="0.02304926" ixy="0.0002468" ixz="-0.00026556" iyy="0.05493657" iyz="5.697e-05" izz="0.06847865"/>
    </inertial>
  </link>
  <!-- Imu is fixed to the base link -->
  <joint name="unitree_imu_joint" type="fixed">
    <origin rpy="0. 0. 0." xyz="0. 0. 0."/>
    <parent link="base"/>
    <child link="unitree_imu"/>
  </joint>
  <!-- Imu link -->
  <link name="unitree_imu">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.000001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.000001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.015 0.015 0.004"/>
      </geometry>
    </visual>
    <material name="orange">
      <color rgba="255 108 10 255"/>
    </material>
  </link>

  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.2 -0.06 -0.0"/>
    <parent link="base"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.45" upper="0.45" velocity="30.9971"/>
  </joint>
  <link name="FR_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <!-- <origin rpy="${pi} 0 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="../meshes_ysc/R_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00339594 0.01067293 -8.467e-05"/>
      <mass value="0.56914725"/>
      <inertia ixx="0.00031423" ixy="-2.734e-05" ixz="4e-07" iyy="0.00040314" iyz="1.33e-06" izz="0.00037574"/>
    </inertial>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.0963 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="1.79" velocity="30.9971"/>
  </joint>
  <link name="FR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/R_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.18 0.03 0.03"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00352314 0.0239451 -0.02373849"/>
      <mass value="1.01896172"/>
      <inertia ixx="0.0036974" ixy="-8.428e-05" ixz="-0.0002695" iyy="0.00342458" iyz="-0.00052094" izz="0.00112813"/>
    </inertial>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-2.6195" upper="-0.9" velocity="30.9971"/>
  </joint>
  <link name="FR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <geometry>
        <box size="0.18 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00039122 1.444e-05 -0.10612109"/>
      <mass value="0.2000138"/>
      <inertia ixx="0.0011472" ixy="2e-08" ixz="4.418e-05" iyy="0.00115864" iyz="-3.1e-07" izz="4.163e-05"/>
    </inertial>
  </link>
  <joint name="FR_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="FR_calf"/>
    <child link="FR_foot"/>
  </joint>
  <link name="FR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_redgo_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.09e-05" ixy="0" ixz="0" iyy="1.316e-05" iyz="0" izz="1.277e-05"/>
    </inertial>
  </link>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.2 0.06 -0.0"/>
    <parent link="base"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.45" upper="0.45" velocity="30.9971"/>
  </joint>
  <link name="FL_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00339594 -0.01067293 -8.467e-05"/>
      <mass value="0.56914725"/>
      <inertia ixx="0.00031423" ixy="2.734e-05" ixz="4e-07" iyy="0.00040314" iyz="-1.33e-06" izz="0.00037574"/>
    </inertial>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.0963 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="1.79" velocity="30.9971"/>
  </joint>
  <link name="FL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.18 0.03 0.03"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00352314 -0.0239451 -0.02373849"/>
      <mass value="1.01896172"/>
      <inertia ixx="0.0036974" ixy="8.428e-05" ixz="-0.0002695" iyy="0.00342458" iyz="0.00052094" izz="0.00112813"/>
    </inertial>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-2.6195" upper="-0.9" velocity="30.9971"/>
  </joint>
  <link name="FL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <geometry>
        <box size="0.18 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00039122 1.444e-05 -0.10612109"/>
      <mass value="0.2000138"/>
      <inertia ixx="0.0011472" ixy="2e-08" ixz="4.418e-05" iyy="0.00115864" iyz="-3.1e-07" izz="4.163e-05"/>
    </inertial>
  </link>
  <joint name="FL_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="FL_calf"/>
    <child link="FL_foot"/>
  </joint>
  <link name="FL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_redgo_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.09e-05" ixy="0" ixz="0" iyy="1.316e-05" iyz="0" izz="1.277e-05"/>
    </inertial>
  </link>

  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.2 -0.06 -0.0"/>
    <parent link="base"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.45" upper="0.45" velocity="30.9971"/>
  </joint>
  <link name="RR_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <!-- <origin rpy="${pi} ${pi} 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="../meshes_ysc/L_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="0.00339594 0.01067293 -8.467e-05"/>
      <mass value="0.56914725"/>
      <inertia ixx="0.00031423" ixy="2.734e-05" ixz="-4e-07" iyy="0.00040314" iyz="1.33e-06" izz="0.00037574"/>
    </inertial>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.0963 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="1.79" velocity="30.9971"/>
  </joint>
  <link name="RR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/R_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.18 0.03 0.03"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00352314 0.0239451 -0.02373849"/>
      <mass value="1.01896172"/>
      <inertia ixx="0.0036974" ixy="-8.428e-05" ixz="-0.0002695" iyy="0.00342458" iyz="-0.00052094" izz="0.00112813"/>
    </inertial>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-2.6195" upper="-0.9" velocity="30.9971"/>
  </joint>
  <link name="RR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <geometry>
        <box size="0.18 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00039122 1.444e-05 -0.10612109"/>
      <mass value="0.2000138"/>
      <inertia ixx="0.0011472" ixy="2e-08" ixz="4.418e-05" iyy="0.00115864" iyz="-3.1e-07" izz="4.163e-05"/>
    </inertial>
  </link>
  <joint name="RR_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="RR_calf"/>
    <child link="RR_foot"/>
  </joint>
  <link name="RR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_redgo_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <inertia
                        ixx="${(2*foot_mass)/5.0*(foot_radius*foot_radius)}" ixy="0.0" ixz="0.0"
                        iyy="${(2*foot_mass)/5.0*(foot_radius*foot_radius)}" iyz="0.0"
                        izz="${(2*foot_mass)/5.0*(foot_radius*foot_radius)}"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <inertial>
      <mass value="0.0450952"/>
      <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.09e-05" ixy="0" ixz="0" iyy="1.316e-05" iyz="0" izz="1.277e-05"/>
    </inertial>
  </link>

  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.2 0.06 -0.0"/>
    <parent link="base"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-0.45" upper="0.45" velocity="30.9971"/>
  </joint>
  <link name="RL_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <!-- <origin rpy="0 ${pi} 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="../meshes_ysc/R_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="0.00339594 -0.01067293 -8.467e-05"/>
      <mass value="0.56914725"/>
      <inertia ixx="0.00031423" ixy="-2.734e-05" ixz="-4e-07" iyy="0.00040314" iyz="-1.33e-06" izz="0.00037574"/>
    </inertial>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.0963 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-1" upper="1.79" velocity="30.9971"/>
  </joint>
  <link name="RL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <!-- <origin rpy="0 ${pi/2.0+0.1} 0" xyz="0 0 ${-thigh_length/2.0}"/> -->
      <geometry>
        <box size="0.18 0.03 0.03"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.035" radius="0.035"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00352314 -0.0239451 -0.02373849"/>
      <mass value="1.01896172"/>
      <inertia ixx="0.0036974" ixy="8.428e-05" ixz="-0.0002695" iyy="0.00342458" iyz="0.00052094" izz="0.00112813"/>
    </inertial>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="19.94" lower="-2.6195" upper="-0.9" velocity="30.9971"/>
  </joint>
  <link name="RL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes_ysc/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.09"/>
      <geometry>
        <box size="0.18 0.018 0.015"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00039122 1.444e-05 -0.10612109"/>
      <mass value="0.2000138"/>
      <inertia ixx="0.0011472" ixy="2e-08" ixz="4.418e-05" iyy="0.00115864" iyz="-3.1e-07" izz="4.163e-05"/>
    </inertial>
  </link>
  <joint name="RL_foot_fixed" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.18"/>
    <parent link="RL_calf"/>
    <child link="RL_foot"/>
  </joint>
  <link name="RL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_redgo_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="gold"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <inertia
                        ixx="${(2*foot_mass)/5.0*(foot_radius*foot_radius)}" ixy="0.0" ixz="0.0"
                        iyy="${(2*foot_mass)/5.0*(foot_radius*foot_radius)}" iyz="0.0"
                        izz="${(2*foot_mass)/5.0*(foot_radius*foot_radius)}"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <inertial>
      <mass value="0.0450952"/>
      <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.09e-05" ixy="0" ixz="0" iyy="1.316e-05" iyz="0" izz="1.277e-05"/>
    </inertial>
  </link>

  <!-- Robot Footprint
  <joint name="footprint_joint" type="fixed">
    <parent link="base"/>
    <child link="base_footprint"/>
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.2"/>
  </joint>
  <link name="base_footprint">
    <inertial>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
      <mass value="0.001"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link> -->
  <!-- <xacro:lidar parent="base" xyz="-0.065 0. 0.1" rpy="0. 0. 0."/> -->
</robot>

