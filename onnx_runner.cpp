
#include <onnxruntime_cxx_api.h>
#include <iostream>
#include <vector>
#include <numeric>

// 一个简单的结构体来模拟您代码中的数据
struct PropriState {
    std::vector<float> baseAngVel = {0.1f, 0.2f, 0.3f};
    std::vector<float> projectedGravity = {0.4f, 0.5f, 0.6f};
    std::vector<float> jointPos = {0.1f, 0.2f, 0.3f, 0.4f, 0.5f, 0.6f, 0.7f, 0.8f, 0.9f, 1.0f, 1.1f, 1.2f};
    std::vector<float> jointVel = {0.1f, 0.2f, 0.3f, 0.4f, 0.5f, 0.6f, 0.7f, 0.8f, 0.9f, 1.0f, 1.1f, 1.2f};
};

struct ObsScales {
    float angVel = 0.5f;
    float dofPos = 0.8f;
    float dofVel = 0.6f;
};


int main() {
    // 1. 初始化ONNX Runtime环境
    Ort::Env env(ORT_LOGGING_LEVEL_WARNING, "ONNX-Runner");
    Ort::SessionOptions session_options;
    session_options.SetIntraOpNumThreads(1);
    session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);

    // 2. 加载ONNX模型
    const char* model_path = "mlp_barlow_twins_actor.onnx";
    Ort::Session session(env, model_path, session_options);

    // 3. 准备输入数据
    // 假设您已经有了这些变量
    PropriState propri_;
    ObsScales obsScales;
    float commandScaler = 1.0f;
    std::vector<float> command = {0.1, 0.2, 0.3};
    std::vector<float> defaultJointAnglesRL_ = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f};
    std::vector<float> prev_actions = {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f}; // 假设的先前动作

    // 构建 'obs' 张量 (1, 45)
    std::vector<float> obs_input_data;
    obs_input_data.reserve(45);

    for(float val : propri_.baseAngVel) obs_input_data.push_back(val * obsScales.angVel);
    for(float val : propri_.projectedGravity) obs_input_data.push_back(val);
    for(float val : command) obs_input_data.push_back(val * commandScaler);
    for(size_t i = 0; i < propri_.jointPos.size(); ++i) obs_input_data.push_back((propri_.jointPos[i] - defaultJointAnglesRL_[i]) * obsScales.dofPos);
    for(float val : propri_.jointVel) obs_input_data.push_back(val * obsScales.dofVel);
    obs_input_data.insert(obs_input_data.end(), prev_actions.begin(), prev_actions.end());


    // 构建 'obs_hist' 张量 (1, 10, 45)
    // 在这个例子中, 我们用当前的obs填充历史记录
    std::vector<float> obs_hist_input_data;
    obs_hist_input_data.reserve(10 * 45);
    for(int i = 0; i < 10; ++i) {
        obs_hist_input_data.insert(obs_hist_input_data.end(), obs_input_data.begin(), obs_input_data.end());
    }

    // 4. 创建输入张量
    Ort::AllocatorWithDefaultOptions allocator;

    std::vector<int64_t> obs_shape = {1, 45};
    std::vector<int64_t> obs_hist_shape = {1, 10, 45};

    Ort::Value obs_tensor = Ort::Value::CreateTensor<float>(allocator, obs_input_data.data(), obs_input_data.size(), obs_shape.data(), obs_shape.size());
    Ort::Value obs_hist_tensor = Ort::Value::CreateTensor<float>(allocator, obs_hist_input_data.data(), obs_hist_input_data.size(), obs_hist_shape.data(), obs_hist_shape.size());

    // 5. 定义输入输出节点名称
    const char* input_names[] = {"obs", "obs_hist"};
    const char* output_names[] = {"mean"};

    // 6. 执行推理
    std::vector<Ort::Value> ort_inputs;
    ort_inputs.push_back(std::move(obs_tensor));
    ort_inputs.push_back(std::move(obs_hist_tensor));

    auto output_tensors = session.Run(Ort::RunOptions{nullptr}, input_names, ort_inputs.data(), ort_inputs.size(), output_names, 1);

    // 7. 获取输出
    float* output_data = output_tensors[0].GetTensorMutableData<float>();
    size_t output_size = output_tensors[0].GetTensorTypeAndShapeInfo().GetElementCount();

    // 打印输出
    std::cout << "ONNX Model Output (mean):" << std::endl;
    for (size_t i = 0; i < output_size; ++i) {
        std::cout << output_data[i] << " ";
    }
    std::cout << std::endl;

    return 0;
}
